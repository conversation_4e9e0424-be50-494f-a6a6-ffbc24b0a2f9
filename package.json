{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bytemd/plugin-breaks": "^1.22.0", "@bytemd/plugin-frontmatter": "^1.22.0", "@bytemd/plugin-gemoji": "^1.22.0", "@bytemd/plugin-gfm": "^1.22.0", "@bytemd/plugin-highlight-ssr": "^1.22.0", "@bytemd/plugin-medium-zoom": "^1.22.0", "@bytemd/react": "^1.22.0", "@heroui/react": "2.8.0-beta.7", "@iconify-json/fa6-brands": "^1.2.5", "@iconify-json/logos": "^1.2.4", "@iconify-json/lucide": "^1.2.43", "@iconify-json/skill-icons": "^1.2.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@types/lodash-es": "^4.17.12", "bytemd": "^1.22.0", "clsx": "^2.1.1", "framer-motion": "^12.17.3", "geist": "^1.4.2", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "medium-zoom": "^1.1.0", "next": "15.3.2", "next-themes": "^0.4.6", "radix-ui": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-type-animation": "^3.2.0", "rehype-slug": "^6.0.0", "tailwind-merge": "^3.3.0", "tailwindcss-animated": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify/tailwind4": "^1.0.6", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}